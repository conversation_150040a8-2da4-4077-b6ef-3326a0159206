#!/usr/bin/env python3
"""
YAA (YET ANOTHER AGENT) - Teste de Debug para OHLCV
Teste específico para identificar o erro "sequence index must be integer, not 'str'"
"""

import asyncio
import logging
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.market.base_integration import CryptoDataFetcher
from qualia.common.specs import MarketSpec

# Configurar logging
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_ohlcv_debug():
    """Teste específico para debug do erro OHLCV."""
    print("🔍 YAA-DEBUG: Iniciando teste de debug OHLCV...")
    
    try:
        # Inicializar CryptoDataFetcher para Binance
        fetcher = CryptoDataFetcher(
            exchange_id="binance",
            api_key="",  # Não precisa de chaves para dados públicos
            api_secret="",
            sandbox=False
        )
        
        await fetcher.initialize()
        print("✅ YAA-DEBUG: CryptoDataFetcher inicializado")
        
        # Testar fetch_ohlcv com debugging detalhado
        spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
        print(f"🔍 YAA-DEBUG: Testando fetch_ohlcv para {spec.symbol} {spec.timeframe}")
        
        try:
            df = await fetcher.fetch_ohlcv(spec, limit=5)
            print(f"✅ YAA-DEBUG: fetch_ohlcv completado - Shape: {df.shape}")
            print(f"✅ YAA-DEBUG: Colunas: {list(df.columns)}")
            if not df.empty:
                print(f"✅ YAA-DEBUG: Primeira linha: {df.iloc[0].to_dict()}")
        except Exception as e:
            print(f"❌ YAA-DEBUG: Erro no fetch_ohlcv: {e}")
            print(f"❌ YAA-DEBUG: Tipo do erro: {type(e).__name__}")
            import traceback
            print(f"❌ YAA-DEBUG: Stack trace: {traceback.format_exc()}")
        
        await fetcher.close()
        
    except Exception as e:
        print(f"❌ YAA-DEBUG: Erro geral: {e}")
        import traceback
        print(f"❌ YAA-DEBUG: Stack trace geral: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_ohlcv_debug())
