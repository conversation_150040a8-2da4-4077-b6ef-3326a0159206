#!/usr/bin/env python3
"""
Teste específico para verificar a consistência dos logs de timeframes.

YAA-FIX: Simula o comportamento do sistema de scalping para verificar
se os logs mostram os timeframes corretos.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
from qualia.strategies.strategy_interface import TradingContext
from qualia.utils.logger import get_logger

# Configurar logging para capturar os logs da estratégia
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = get_logger(__name__)

def create_mock_ohlcv_data(periods: int = 100, base_price: float = 200.0) -> pd.DataFrame:
    """Cria dados OHLCV mock para teste."""
    dates = pd.date_range(start=datetime.now() - timedelta(minutes=periods), 
                         end=datetime.now(), freq='1min')
    
    # Gerar dados realistas com alguma volatilidade
    np.random.seed(42)  # Para resultados reproduzíveis
    price_changes = np.random.normal(0, 0.002, len(dates))  # 0.2% volatilidade
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = {
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def simulate_scalping_system():
    """Simula o comportamento do sistema de scalping."""
    print("🎯 Simulando Sistema de Scalping - Teste de Logs")
    print("=" * 60)
    
    # Configuração da estratégia
    config = {
        'fibonacci_wave_hype_config': {
            'params': {
                'fib_lookback': 20,
                'supported_timeframes': ['1m', '5m', '15m', '1h'],
                'primary_timeframe': '1m'
            }
        }
    }
    
    # Inicializar estratégia
    strategy = FibonacciWaveHypeStrategy(
        symbol="SOL/USDT",
        timeframe="1m",
        parameters=config['fibonacci_wave_hype_config']['params']
    )
    
    # Dados mock
    ohlcv_data = create_mock_ohlcv_data(100, 200.0)
    
    # Simular o comportamento do sistema de scalping
    # que chama a estratégia para cada timeframe individualmente
    timeframes = ['1m', '5m', '15m', '1h']
    symbol = "SOL/USDT"
    
    print(f"📊 Análise multi-timeframe para {symbol}")
    
    timeframe_signals = {}
    
    for timeframe in timeframes:
        print(f"\n🔍 Processando timeframe: {timeframe}")
        
        try:
            # Simular obtenção de dados de mercado
            market_data = {
                'price': 200.0,
                'volume': 5000.0,
                'timestamp': datetime.now()
            }
            
            # Criar contexto específico para o timeframe
            context = TradingContext(
                symbol=symbol,
                timeframe=timeframe,  # Timeframe específico
                current_price=market_data['price'],
                timestamp=pd.Timestamp.now(),
                ohlcv=ohlcv_data,
                wallet_state={'balance': 10000.0, 'positions': {}}
            )
            
            # Gerar sinal (isso deve usar o novo método single-timeframe)
            signal_df = strategy.generate_signal(context)
            
            if signal_df is not None and not signal_df.empty:
                signal_action = signal_df.iloc[0].get('signal', 'HOLD')
                confidence = signal_df.iloc[0].get('confidence', 0.0)
                
                timeframe_signals[timeframe] = {
                    'action': signal_action,
                    'confidence': confidence
                }
                
                print(f"   {timeframe}: {signal_action} (confiança: {confidence:.3f})")
            else:
                timeframe_signals[timeframe] = {'action': 'HOLD', 'confidence': 0.0}
                print(f"   {timeframe}: HOLD")
                
        except Exception as e:
            print(f"❌ Erro ao processar {symbol} ({timeframe}): {e}")
            continue
    
    # Simular consolidação (simplificada)
    print(f"\n🔄 DEBUG: timeframe_signals={timeframe_signals}")
    
    # Verificar se há sinais válidos
    valid_signals = [tf for tf, sig in timeframe_signals.items() 
                    if sig['action'] != 'HOLD' and sig['confidence'] > 0.0]
    
    if valid_signals:
        print(f"✅ Sinais válidos encontrados em: {valid_signals}")
        # Simular lógica de consolidação
        strongest_signal = max(
            [(tf, sig) for tf, sig in timeframe_signals.items() if sig['action'] != 'HOLD'],
            key=lambda x: x[1]['confidence'],
            default=(None, None)
        )
        
        if strongest_signal[0]:
            tf, sig = strongest_signal
            print(f"🎯 {symbol}: Sinal consolidado = {sig['action']} (confiança: {sig['confidence']:.3f}) - Timeframe líder: {tf}")
        else:
            print(f"🔄 {symbol}: Consolidação final = HOLD (confiança: 0.000) - No signals detected")
    else:
        print(f"🔄 {symbol}: Consolidação final = HOLD (confiança: 0.000) - No signals detected")
    
    print(f"📋 {symbol}: Nenhum sinal consolidado gerado")
    
    return timeframe_signals

def test_log_patterns():
    """Testa se os padrões de log estão corretos."""
    print("\n" + "=" * 60)
    print("🧪 TESTE DE PADRÕES DE LOG")
    print("=" * 60)
    
    # Capturar logs
    import io
    import sys
    
    # Redirecionar stdout para capturar logs
    old_stdout = sys.stdout
    sys.stdout = captured_output = io.StringIO()
    
    try:
        # Executar simulação
        results = simulate_scalping_system()
        
        # Restaurar stdout
        sys.stdout = old_stdout
        
        # Analisar logs capturados
        log_content = captured_output.getvalue()
        
        print("📝 Análise dos logs capturados:")
        print("-" * 40)
        
        # Verificar se os logs mostram timeframes corretos
        timeframes = ['1m', '5m', '15m', '1h']
        log_issues = []
        
        for tf in timeframes:
            # Procurar por padrões incorretos (sempre @1m)
            if f"SOL/USDT@1m:" in log_content and tf != '1m':
                # Verificar se há logs incorretos para outros timeframes
                lines = log_content.split('\n')
                for line in lines:
                    if f"Processando timeframe: {tf}" in line:
                        # Próximas linhas devem mostrar @{tf}, não @1m
                        pass
        
        if not log_issues:
            print("✅ Logs estão consistentes - cada timeframe mostra corretamente")
        else:
            print("❌ Problemas encontrados nos logs:")
            for issue in log_issues:
                print(f"   - {issue}")
        
        return results
        
    except Exception as e:
        sys.stdout = old_stdout
        print(f"❌ Erro no teste: {e}")
        return {}

if __name__ == "__main__":
    print("🎯 QUALIA - Teste de Consistência de Logs")
    print("=" * 60)
    
    # Executar testes
    results = simulate_scalping_system()
    test_results = test_log_patterns()
    
    print("\n" + "=" * 60)
    print("✅ Teste de logs concluído!")
    print("=" * 60)
