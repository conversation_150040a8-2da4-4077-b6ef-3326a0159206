#!/usr/bin/env python3
"""
Teste para verificar a correção dos timeframes misturados no sistema QUALIA.

YAA-FIX: Teste específico para validar que cada timeframe é processado corretamente.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
from qualia.strategies.strategy_interface import TradingContext
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_mock_ohlcv_data(periods: int = 100, base_price: float = 200.0) -> pd.DataFrame:
    """Cria dados OHLCV mock para teste."""
    dates = pd.date_range(start=datetime.now() - timedelta(minutes=periods), 
                         end=datetime.now(), freq='1min')
    
    # Gerar dados realistas com alguma volatilidade
    np.random.seed(42)  # Para resultados reproduzíveis
    price_changes = np.random.normal(0, 0.002, len(dates))  # 0.2% volatilidade
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = {
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def test_single_timeframe_processing():
    """Testa se cada timeframe é processado corretamente."""
    print("🧪 Testando processamento individual de timeframes...")
    
    try:
        # Configuração mínima para teste
        config = {
            'fibonacci_wave_hype_config': {
                'params': {
                    'fib_lookback': 20,
                    'supported_timeframes': ['1m', '5m', '15m', '1h'],
                    'primary_timeframe': '1m'
                }
            }
        }
        
        # Inicializar estratégia
        strategy = FibonacciWaveHypeStrategy(
            symbol="SOL/USDT",
            timeframe="1m",
            parameters=config['fibonacci_wave_hype_config']['params']
        )
        
        # Dados mock
        ohlcv_data = create_mock_ohlcv_data(100, 200.0)
        
        # Testar cada timeframe individualmente
        timeframes_to_test = ['1m', '5m', '15m', '1h']
        results = {}
        
        for tf in timeframes_to_test:
            print(f"\n📊 Testando timeframe: {tf}")
            
            # Criar contexto específico para o timeframe
            context = TradingContext(
                symbol="SOL/USDT",
                timeframe=tf,  # Timeframe específico
                current_price=200.0,
                timestamp=pd.Timestamp.now(),
                ohlcv=ohlcv_data,
                wallet_state={'balance': 10000.0, 'positions': {}}
            )
            
            # Gerar sinal
            signal_df = strategy.generate_signal(context)
            
            # Verificar resultado
            if not signal_df.empty:
                signal_data = signal_df.iloc[0]
                results[tf] = {
                    'signal': signal_data.get('signal', 'HOLD'),
                    'confidence': signal_data.get('confidence', 0.0),
                    'timeframe': signal_data.get('timeframe', 'unknown')
                }
                print(f"✅ {tf}: {results[tf]['signal']} (conf: {results[tf]['confidence']:.3f}) - TF: {results[tf]['timeframe']}")
            else:
                results[tf] = {'signal': 'HOLD', 'confidence': 0.0, 'timeframe': tf}
                print(f"✅ {tf}: HOLD (sem sinal gerado)")
        
        # Verificar se os timeframes estão corretos
        print("\n🔍 Verificação de timeframes:")
        all_correct = True
        for tf, result in results.items():
            expected_tf = result.get('timeframe', tf)
            if expected_tf == tf:
                print(f"✅ {tf}: Timeframe correto")
            else:
                print(f"❌ {tf}: Timeframe incorreto (esperado: {tf}, obtido: {expected_tf})")
                all_correct = False
        
        if all_correct:
            print("\n🎉 SUCESSO: Todos os timeframes foram processados corretamente!")
        else:
            print("\n❌ FALHA: Alguns timeframes não foram processados corretamente.")
        
        return results
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_multi_timeframe_vs_single():
    """Compara processamento multi-timeframe vs single-timeframe."""
    print("\n🔄 Testando diferença entre multi-timeframe e single-timeframe...")
    
    try:
        # Configuração
        config = {
            'fibonacci_wave_hype_config': {
                'params': {
                    'fib_lookback': 20,
                    'supported_timeframes': ['1m', '5m', '15m', '1h'],
                    'primary_timeframe': '1m'
                }
            }
        }
        
        strategy = FibonacciWaveHypeStrategy(
            symbol="SOL/USDT",
            timeframe="1m",
            parameters=config['fibonacci_wave_hype_config']['params']
        )
        
        ohlcv_data = create_mock_ohlcv_data(100, 200.0)
        
        # Teste 1: Multi-timeframe (timeframe = primary_timeframe)
        context_multi = TradingContext(
            symbol="SOL/USDT",
            timeframe="1m",  # Mesmo que primary_timeframe
            current_price=200.0,
            timestamp=pd.Timestamp.now(),
            ohlcv=ohlcv_data,
            wallet_state={'balance': 10000.0, 'positions': {}}
        )
        
        print("📊 Teste multi-timeframe (1m = primary)...")
        signal_multi = strategy.generate_signal(context_multi)
        
        # Teste 2: Single-timeframe (timeframe != primary_timeframe)
        context_single = TradingContext(
            symbol="SOL/USDT",
            timeframe="5m",  # Diferente do primary_timeframe
            current_price=200.0,
            timestamp=pd.Timestamp.now(),
            ohlcv=ohlcv_data,
            wallet_state={'balance': 10000.0, 'positions': {}}
        )
        
        print("📊 Teste single-timeframe (5m != primary)...")
        signal_single = strategy.generate_signal(context_single)
        
        print(f"Multi-timeframe result: {len(signal_multi)} rows")
        print(f"Single-timeframe result: {len(signal_single)} rows")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste comparativo: {e}")
        return False

if __name__ == "__main__":
    print("🎯 QUALIA - Teste de Correção de Timeframes")
    print("=" * 50)
    
    # Executar testes
    results = test_single_timeframe_processing()
    test_multi_timeframe_vs_single()
    
    print("\n" + "=" * 50)
    print("✅ Testes concluídos!")
